<?php
/**
 * Classe per la gestione del frontend
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

class CPV_Frontend {
    
    /**
     * Istanza della classe Database
     */
    private $database;

    /**
     * Istanza della classe Settings
     */
    private $settings;
    
    /**
     * Costruttore
     */
    public function __construct() {
        $this->database = new CPV_Database();
        $this->settings = CPV_Settings::get_instance();
        $this->init_hooks();
    }
    
    /**
     * Inizializza gli hook per il frontend
     */
    private function init_hooks() {
        // Hook per visualizzare le varianti nella pagina prodotto
        add_action('woocommerce_single_product_summary', array($this, 'display_product_variants'), 25);
        
        // Hook per caricare gli script e stili frontend
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        
        // Hook per le chiamate AJAX del frontend
        add_action('wp_ajax_cpv_get_variant_price', array($this, 'ajax_get_variant_price'));
        add_action('wp_ajax_nopriv_cpv_get_variant_price', array($this, 'ajax_get_variant_price'));
        add_action('wp_ajax_cpv_format_price', array($this, 'ajax_format_price'));
        add_action('wp_ajax_nopriv_cpv_format_price', array($this, 'ajax_format_price'));
        
        // Hook per modificare il prezzo visualizzato
        add_filter('woocommerce_get_price_html', array($this, 'modify_price_display'), 10, 2);
    }
    
    /**
     * Visualizza le varianti nella pagina prodotto
     */
    public function display_product_variants() {
        global $product;
        
        if (!$product) {
            return;
        }
        
        $product_id = $product->get_id();
        $variants = $this->database->get_product_variants($product_id, true);
        
        if (empty($variants)) {
            return;
        }

        // Ottiene le impostazioni del layout
        $layout_style = $this->settings->get_setting('layout_style', 'vertical');
        $show_variant_count = $this->settings->get_setting('show_variant_count', true);
        $enable_hover_effects = $this->settings->get_setting('enable_hover_effects', true);

        // Classi CSS dinamiche
        $container_classes = array(
            'cpv-product-variants-container',
            'cpv-layout-' . $layout_style
        );

        if (!$enable_hover_effects) {
            $container_classes[] = 'cpv-no-hover-effects';
        }

        $variants_count = count($variants);

        ?>
        <div class="<?php echo esc_attr(implode(' ', $container_classes)); ?>">
            <h3 class="cpv-variants-title">
                <?php _e('Varianti Disponibili', 'custom-product-variants'); ?>
                <?php if ($show_variant_count): ?>
                    <span class="cpv-variants-count">(<?php echo $variants_count; ?>)</span>
                <?php endif; ?>
            </h3>
            <p class="cpv-variants-description"><?php _e('Seleziona una variante per personalizzare il tuo acquisto:', 'custom-product-variants'); ?></p>

            <div class="cpv-variants-grid cpv-layout-<?php echo esc_attr($layout_style); ?>">
                <?php foreach ($variants as $variant): ?>
                    <?php echo $this->render_variant_card($variant, $layout_style); ?>
                <?php endforeach; ?>
            </div>
            
            <div class="cpv-variant-selection-info" style="display: none;">
                <p class="cpv-selected-variant-text">
                    <?php _e('Variante selezionata:', 'custom-product-variants'); ?> 
                    <strong class="cpv-selected-variant-name"></strong> - 
                    <span class="cpv-selected-variant-price"></span>
                </p>
                <button type="button" class="cpv-clear-selection button">
                    <?php _e('Rimuovi Selezione', 'custom-product-variants'); ?>
                </button>
            </div>
        </div>
        
        <script type="text/javascript">
            // Passa i dati del prodotto al JavaScript
            if (typeof cpv_product_data === 'undefined') {
                var cpv_product_data = {
                    product_id: <?php echo json_encode($product_id); ?>,
                    original_price: <?php echo json_encode($product->get_price()); ?>,
                    variants: <?php echo json_encode($variants); ?>
                };
            }
        </script>
        <?php
    }
    
    /**
     * Carica gli script e stili per il frontend
     */
    public function enqueue_frontend_scripts() {
        // Carica solo nelle pagine prodotto
        if (!is_product()) {
            return;
        }
        
        // Carica gli stili CSS
        wp_enqueue_style(
            'cpv-frontend-css',
            CPV_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            CPV_PLUGIN_VERSION
        );
        
        // Carica gli script JavaScript
        wp_enqueue_script(
            'cpv-frontend-js',
            CPV_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            CPV_PLUGIN_VERSION,
            true
        );
        
        // Localizza lo script con le variabili necessarie
        wp_localize_script('cpv-frontend-js', 'cpv_frontend', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cpv_frontend_nonce'),
            'strings' => array(
                'select_variant' => __('Seleziona una variante', 'custom-product-variants'),
                'variant_selected' => __('Variante selezionata:', 'custom-product-variants'),
                'original_product' => __('Prodotto originale', 'custom-product-variants'),
                'loading' => __('Caricamento...', 'custom-product-variants'),
                'error' => __('Si è verificato un errore.', 'custom-product-variants'),
            )
        ));
    }
    
    /**
     * AJAX: Ottiene il prezzo di una variante
     */
    public function ajax_get_variant_price() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_frontend_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }
        
        $variant_id = intval($_POST['variant_id']);
        
        if (!$variant_id) {
            wp_send_json_error(__('ID variante non valido.', 'custom-product-variants'));
        }
        
        $variant = $this->database->get_variant($variant_id);
        
        if (!$variant || !$variant->is_active) {
            wp_send_json_error(__('Variante non trovata o non attiva.', 'custom-product-variants'));
        }
        
        wp_send_json_success(array(
            'variant_id' => $variant->id,
            'variant_name' => $variant->variant_name,
            'variant_price' => $variant->variant_price,
            'variant_price_html' => wc_price($variant->variant_price)
        ));
    }

    /**
     * AJAX: Formatta un prezzo
     */
    public function ajax_format_price() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_frontend_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        $price = floatval($_POST['price']);

        if ($price < 0) {
            wp_send_json_error(__('Prezzo non valido.', 'custom-product-variants'));
        }

        wp_send_json_success(array(
            'formatted_price' => wc_price($price)
        ));
    }
    
    /**
     * Modifica il prezzo visualizzato quando ci sono varianti attive
     */
    public function modify_price_display($price_html, $product) {
        // Verifica se siamo in una pagina prodotto singola
        if (!is_product() || !$product) {
            return $price_html;
        }

        $product_id = $product->get_id();

        // Verifica se il prodotto ha varianti attive
        if ($this->product_has_variants($product_id)) {
            // Se il prodotto ha varianti attive, nascondi completamente il prezzo originale
            // Il prezzo sarà mostrato solo quando una variante è selezionata
            return '<div class="cpv-price-hidden" data-original-price="' . esc_attr($price_html) . '" style="display: none;"></div>';
        }

        // Se non ci sono varianti, mostra il prezzo normale
        return $price_html;
    }
    
    /**
     * Verifica se un prodotto ha varianti attive
     */
    public function product_has_variants($product_id) {
        $count = $this->database->count_product_variants($product_id, true);
        return $count > 0;
    }
    
    /**
     * Ottiene le varianti attive di un prodotto
     */
    public function get_product_active_variants($product_id) {
        return $this->database->get_product_variants($product_id, true);
    }
    
    /**
     * Ottiene una variante specifica se è attiva
     */
    public function get_active_variant($variant_id) {
        $variant = $this->database->get_variant($variant_id);
        
        if ($variant && $variant->is_active) {
            return $variant;
        }
        
        return null;
    }
    
    /**
     * Formatta il prezzo di una variante
     */
    public function format_variant_price($price) {
        return wc_price($price);
    }
    
    /**
     * Genera il markup HTML per una singola variante
     */
    public function render_variant_card($variant, $layout_style = 'vertical') {
        ob_start();

        $card_classes = array(
            'cpv-variant-card',
            'cpv-layout-' . $layout_style
        );

        // Aggiungi classe specifica se non c'è immagine
        if (empty($variant->variant_image)) {
            $card_classes[] = 'cpv-no-image';
        }

        ?>
        <div class="<?php echo esc_attr(implode(' ', $card_classes)); ?>" data-variant-id="<?php echo esc_attr($variant->id); ?>" data-variant-price="<?php echo esc_attr($variant->variant_price); ?>">
            <?php if (!empty($variant->variant_image)): ?>
                <div class="cpv-variant-image">
                    <img src="<?php echo esc_url($variant->variant_image); ?>" alt="<?php echo esc_attr($variant->variant_name); ?>">
                </div>
            <?php endif; ?>

            <div class="cpv-variant-content">
                <div class="cpv-variant-info">
                    <h4 class="cpv-variant-name"><?php echo esc_html($variant->variant_name); ?></h4>
                    <div class="cpv-variant-price"><?php echo wc_price($variant->variant_price); ?></div>
                </div>

                <div class="cpv-variant-select">
                    <input type="radio" name="cpv_selected_variant" id="cpv_variant_<?php echo esc_attr($variant->id); ?>" value="<?php echo esc_attr($variant->id); ?>" class="cpv-variant-radio">
                    <label for="cpv_variant_<?php echo esc_attr($variant->id); ?>" class="cpv-variant-label">
                        <?php _e('Seleziona', 'custom-product-variants'); ?>
                    </label>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
