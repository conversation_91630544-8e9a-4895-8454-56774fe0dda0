# Varianti Prodotto Personalizzate

Un plugin WordPress per WooCommerce che permette la creazione e gestione di varianti personalizzate di prodotti con visualizzazione e acquisto diretto dalla pagina prodotto.

## Descrizione

Il plugin "Varianti Prodotto Personalizzate" estende le funzionalità di WooCommerce permettendo di creare varianti personalizzate per i prodotti che vengono visualizzate come card selezionabili nella pagina prodotto. Quando una variante viene selezionata, sostituisce il prodotto principale nel carrello con il prezzo e le informazioni specifiche della variante.

## 🆕 Novità Implementate

### ✅ Problema 1 Risolto: Funzionalità di Modifica Completa
- **Modal di modifica professionale** per modificare varianti esistenti
- **Pulsante "Modifica" completamente funzionale** con interfaccia intuitiva
- **Aggiornamento in tempo reale** della lista senza ricaricare la pagina
- **Validazione completa** dei dati durante la modifica
- **Gestione immagini** nel modal di modifica

### ✅ Problema 2 Risolto: Gestione Intelligente del Prezzo
- **Prezzo completamente nascosto**: quando esistono varianti attive (non più messaggio placeholder)
- **Prezzo normale**: mostrato quando non ci sono varianti per il prodotto
- **Compatibilità totale** con prodotti semplici e variabili WooCommerce
- **🆕 Gestione ottimizzata immagini**: nessun placeholder quando l'immagine non è presente

## Caratteristiche Principali

### Pannello Amministrativo
- **Tab dedicato** nella pagina di modifica prodotto di WooCommerce
- **Interfaccia intuitiva** per creare, modificare ed eliminare varianti
- **🆕 Modal di modifica professionale** per modificare varianti esistenti
- **Campi personalizzabili**: Nome, Immagine, Prezzo per ogni variante
- **Gestione ordine** delle varianti tramite drag & drop
- **Attivazione/disattivazione** delle varianti
- **Upload immagini** con anteprima e gestione nel modal
- **Validazione dati** in tempo reale con messaggi di errore
- **🆕 Aggiornamento dinamico** della lista senza ricaricare

### Frontend
- **Card responsive** per visualizzare le varianti
- **Selezione esclusiva** (una variante alla volta)
- **🆕 Gestione intelligente del prezzo**: completamente nascosto quando ci sono varianti
- **🆕 Visualizzazione ottimizzata immagini**: nessun placeholder quando non presente
- **Aggiornamento dinamico** del prezzo visualizzato
- **Integrazione seamless** con il carrello WooCommerce
- **Design moderno** e mobile-friendly

### Integrazione Carrello
- **Sostituzione automatica** del prodotto principale con la variante selezionata
- **Mantenimento compatibilità** con altri plugin WooCommerce
- **Visualizzazione corretta** di nome, prezzo e immagine della variante
- **Metadati ordine** per tracciare le varianti acquistate

## Requisiti di Sistema

- **WordPress**: 5.0 o superiore
- **WooCommerce**: 4.0 o superiore
- **PHP**: 7.4 o superiore
- **MySQL**: 5.6 o superiore

## Installazione

### Installazione Manuale

1. **Scarica** il plugin dal repository
2. **Carica** la cartella `custom-product-variants` nella directory `/wp-content/plugins/`
3. **Attiva** il plugin dal menu 'Plugin' di WordPress
4. **Verifica** che WooCommerce sia installato e attivo

### Installazione via WordPress Admin

1. Vai su **Plugin > Aggiungi nuovo**
2. **Carica** il file ZIP del plugin
3. **Installa** e **attiva** il plugin
4. **Verifica** che WooCommerce sia installato e attivo

## Configurazione

Il plugin non richiede configurazioni particolari. Una volta attivato:

1. Vai su **Prodotti > Tutti i prodotti**
2. **Modifica** un prodotto esistente o creane uno nuovo
3. Troverai il nuovo tab **"Varianti Prodotto"** nei dati del prodotto
4. **Aggiungi** le tue varianti personalizzate

## Utilizzo

### Creazione Varianti

1. **Apri** la pagina di modifica di un prodotto
2. **Clicca** sul tab "Varianti Prodotto"
3. **Compila** il form "Aggiungi Nuova Variante":
   - **Nome Variante**: Nome descrittivo (obbligatorio, max 255 caratteri)
   - **Prezzo Variante**: Prezzo in euro (obbligatorio, > 0)
   - **Immagine Variante**: Immagine opzionale per la variante
4. **Clicca** "Aggiungi Variante"

### Gestione Varianti

- **Modifica**: Clicca il pulsante "Modifica" nella lista delle varianti
- **Attiva/Disattiva**: Usa il pulsante "Attiva/Disattiva" per controllare la visibilità
- **Elimina**: Clicca "Elimina" per rimuovere definitivamente una variante
- **Riordina**: Trascina le righe per cambiare l'ordine di visualizzazione

### Esperienza Cliente

1. Il cliente **visita** la pagina del prodotto
2. **Visualizza** le varianti disponibili come card sotto la descrizione
3. **Seleziona** una variante cliccando sulla card o sul pulsante "Seleziona"
4. Il **prezzo** si aggiorna automaticamente
5. **Aggiunge** al carrello (la variante sostituisce il prodotto principale)

## Struttura File

```
custom-product-variants/
├── custom-product-variants.php    # File principale del plugin
├── uninstall.php                  # Script di disinstallazione
├── README.md                      # Documentazione
├── includes/                      # Classi PHP
│   ├── class-database.php         # Gestione database
│   ├── class-admin.php            # Pannello amministrativo
│   ├── class-frontend.php         # Frontend
│   └── class-cart-integration.php # Integrazione carrello
├── assets/                        # Risorse statiche
│   ├── css/
│   │   ├── admin.css              # Stili admin
│   │   └── frontend.css           # Stili frontend
│   └── js/
│       ├── admin.js               # JavaScript admin
│       └── frontend.js            # JavaScript frontend
└── languages/                     # File di traduzione
    ├── custom-product-variants.pot
    └── custom-product-variants-it_IT.po
```

## Database

Il plugin crea una tabella personalizzata `wp_cpv_product_variants` con la seguente struttura:

- `id`: ID univoco della variante
- `product_id`: ID del prodotto WooCommerce
- `variant_name`: Nome della variante
- `variant_image`: URL dell'immagine
- `variant_price`: Prezzo della variante
- `sort_order`: Ordine di visualizzazione
- `is_active`: Stato attivo/inattivo
- `created_at`: Data di creazione
- `updated_at`: Data ultimo aggiornamento

## Hooks e Filtri

### Hook Azioni
- `cpv_variant_selected`: Quando una variante viene selezionata
- `cpv_variant_cleared`: Quando la selezione viene rimossa

### Filtri
- `cpv_variant_card_html`: Personalizza l'HTML delle card varianti
- `cpv_variant_price_format`: Personalizza la formattazione del prezzo

## Compatibilità

Il plugin è stato testato con:
- **Temi**: Storefront, Astra, OceanWP, Woodmart
- **Plugin**: WooCommerce Subscriptions, WooCommerce Bookings
- **Page Builder**: Elementor, Gutenberg

## Sicurezza

- **Nonce verification** per tutte le operazioni AJAX
- **Capability checks** per verificare i permessi utente
- **Data sanitization** per tutti gli input
- **SQL injection protection** tramite prepared statements
- **XSS protection** tramite escape delle variabili

## Supporto

Per supporto tecnico o segnalazione bug:

1. **Controlla** la documentazione
2. **Verifica** i requisiti di sistema
3. **Disattiva** altri plugin per test di compatibilità
4. **Contatta** il supporto con informazioni dettagliate

## Test e Debug

Per verificare che le modifiche siano state implementate correttamente:

1. **Assicurati** che `WP_DEBUG` sia attivo in `wp-config.php`
2. **Visita** l'URL: `tuosito.com/wp-admin/?cpv_test=run` (sostituisci con il tuo dominio)
3. **Verifica** che tutti i test passino con ✅
4. **Testa** la funzionalità di modifica nel pannello admin
5. **Controlla** la gestione del prezzo nel frontend

### Test Manuali Consigliati

1. **Crea un prodotto** senza varianti → verifica che il prezzo normale sia visibile
2. **Aggiungi una variante** → verifica che appaia "Seleziona una variante per vedere il prezzo"
3. **Seleziona la variante** → verifica che il prezzo si aggiorni
4. **Modifica la variante** → verifica che il modal si apra e funzioni
5. **Rimuovi tutte le varianti** → verifica che il prezzo normale riappaia

## Changelog

### Versione 1.1.3 (Corrente)
- ✅ **Risolto problema prezzo nascosto** - ora il prezzo appare correttamente quando non ci sono varianti
- ✅ **Filtro prezzo ottimizzato** - applicato solo quando necessario per evitare interferenze
- ✅ **Debug migliorato** - log temporanei per diagnosticare problemi

### Versione 1.1.2
- ✅ **Migliorata gestione del prezzo** - ora completamente nascosto quando ci sono varianti
- ✅ **Ottimizzata visualizzazione immagini** - nessun placeholder quando l'immagine non è presente
- ✅ **Layout intelligente** - design ottimizzato per varianti con e senza immagine
- ✅ **Esperienza utente migliorata** - prezzo mostrato solo quando necessario

### Versione 1.1.1
- ✅ **Risolto problema validazione form** - ora non interferisce più con il salvataggio del prodotto
- ✅ **Rimossi attributi 'required'** dai campi varianti per evitare blocchi
- ✅ **Migliorata gestione upload immagini** nel modal di modifica
- ✅ **Aggiunta logica anti-interferenza** con il form principale di WooCommerce

### Versione 1.1.0
- ✅ **Implementata funzionalità di modifica completa** con modal professionale
- ✅ **Risolto problema gestione prezzo** con logica condizionale intelligente
- ✅ **Aggiunto sistema di test** per verificare le funzionalità
- ✅ **Migliorata UX** con aggiornamenti in tempo reale
- ✅ **Validazione avanzata** per tutti i form

### Versione 1.0.0
- Rilascio iniziale
- Gestione completa varianti prodotto
- Integrazione carrello WooCommerce
- Interfaccia admin completa
- Localizzazione italiana

## Licenza

Questo plugin è rilasciato sotto licenza GPL v2 o successiva.

## Crediti

Sviluppato da **JoJoD3v** per estendere le funzionalità di WooCommerce con varianti prodotto personalizzate.

---

**Nota**: Questo plugin richiede WooCommerce per funzionare correttamente. Assicurati di avere WooCommerce installato e attivo prima di utilizzare questo plugin.
