<?php
/**
 * File di test per verificare le funzionalità del plugin
 * Questo file può essere eseguito temporaneamente per testare le modifiche
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

// Test delle funzionalità implementate
function cpv_test_functionality() {
    echo "<h2>Test Funzionalità Plugin Custom Product Variants</h2>";
    
    // Test 1: Verifica che le classi esistano
    echo "<h3>Test 1: Verifica Classi</h3>";
    if (class_exists('CPV_Admin')) {
        echo "✅ Classe CPV_Admin trovata<br>";
    } else {
        echo "❌ Classe CPV_Admin non trovata<br>";
    }
    
    if (class_exists('CPV_Frontend')) {
        echo "✅ Classe CPV_Frontend trovata<br>";
    } else {
        echo "❌ Classe CPV_Frontend non trovata<br>";
    }
    
    if (class_exists('CPV_Database')) {
        echo "✅ Classe CPV_Database trovata<br>";
    } else {
        echo "❌ Classe CPV_Database non trovata<br>";
    }
    
    // Test 2: Verifica che la tabella del database esista
    echo "<h3>Test 2: Verifica Database</h3>";
    if (class_exists('CPV_Database')) {
        $database = new CPV_Database();
        if ($database->table_exists()) {
            echo "✅ Tabella database trovata<br>";
            
            // Mostra informazioni sulla tabella
            $table_info = $database->get_table_info();
            echo "📊 Righe nella tabella: " . $table_info['row_count'] . "<br>";
        } else {
            echo "❌ Tabella database non trovata<br>";
        }
    }
    
    // Test 3: Verifica hook WordPress
    echo "<h3>Test 3: Verifica Hook</h3>";
    if (has_action('woocommerce_single_product_summary', 'display_product_variants')) {
        echo "✅ Hook frontend registrato<br>";
    } else {
        echo "❌ Hook frontend non registrato<br>";
    }
    
    if (has_action('wp_ajax_cpv_add_variant')) {
        echo "✅ Hook AJAX add_variant registrato<br>";
    } else {
        echo "❌ Hook AJAX add_variant non registrato<br>";
    }
    
    if (has_action('wp_ajax_cpv_update_variant')) {
        echo "✅ Hook AJAX update_variant registrato<br>";
    } else {
        echo "❌ Hook AJAX update_variant non registrato<br>";
    }
    
    if (has_action('wp_ajax_cpv_get_variant')) {
        echo "✅ Hook AJAX get_variant registrato<br>";
    } else {
        echo "❌ Hook AJAX get_variant non registrato<br>";
    }
    
    // Test 4: Verifica filtri
    echo "<h3>Test 4: Verifica Filtri</h3>";
    if (has_filter('woocommerce_get_price_html')) {
        echo "✅ Filtro prezzo registrato<br>";
    } else {
        echo "❌ Filtro prezzo non registrato<br>";
    }
    
    // Test 5: Verifica JavaScript
    echo "<h3>Test 5: Verifica JavaScript</h3>";
    echo "<p>Per testare il JavaScript:</p>";
    echo "<ol>";
    echo "<li>Vai alla pagina di modifica di un prodotto</li>";
    echo "<li>Apri la console del browser (F12)</li>";
    echo "<li>Clicca sul tab 'Varianti Prodotto'</li>";
    echo "<li>Verifica che non ci siano errori JavaScript</li>";
    echo "<li>Se hai varianti esistenti, clicca 'Modifica' e verifica che il modal si apra</li>";
    echo "<li>Testa l'upload dell'immagine nel modal - dovrebbe caricare nell'anteprima del modal, non nel form di creazione</li>";
    echo "</ol>";

    echo "<h3>✅ Test completati!</h3>";
    echo "<p><strong>Note:</strong> Se tutti i test sono passati, le modifiche sono state implementate correttamente.</p>";

    echo "<h3>🐛 Debug Upload Immagini</h3>";
    echo "<p>Se l'upload delle immagini nel modal non funziona correttamente:</p>";
    echo "<ol>";
    echo "<li>Apri la console del browser</li>";
    echo "<li>Clicca 'Modifica' su una variante</li>";
    echo "<li>Clicca 'Carica Immagine' nel modal</li>";
    echo "<li>Verifica i log nella console che iniziano con 'CPV:'</li>";
    echo "<li>Dovrebbe mostrare 'isEditModal: true' quando clicchi nel modal</li>";
    echo "</ol>";

    echo "<h3>🔧 Test Validazione Form</h3>";
    echo "<p><strong>IMPORTANTE:</strong> Verifica che la validazione delle varianti non blocchi il salvataggio del prodotto:</p>";
    echo "<ol>";
    echo "<li>Vai alla pagina di modifica di un prodotto</li>";
    echo "<li>Modifica il <strong>nome del prodotto</strong> o la <strong>descrizione</strong></li>";
    echo "<li>Clicca sul tab 'Varianti Prodotto' (se hai varianti)</li>";
    echo "<li>Torna al tab 'Generale'</li>";
    echo "<li>Clicca <strong>'Aggiorna'</strong> per salvare il prodotto</li>";
    echo "<li>✅ Il prodotto dovrebbe salvarsi <strong>senza problemi</strong></li>";
    echo "<li>❌ Se non si salva, controlla la console per errori</li>";
    echo "</ol>";
    echo "<p><em>Nota: I campi delle varianti ora NON hanno più l'attributo 'required' per evitare interferenze.</em></p>";

    echo "<h3>🎨 Test Gestione Prezzo e Immagini</h3>";
    echo "<p><strong>Verifica la nuova gestione del prezzo e delle immagini:</strong></p>";
    echo "<ol>";
    echo "<li><strong>Prodotto SENZA varianti:</strong>";
    echo "<ul><li>Il prezzo del prodotto deve essere <strong>visibile normalmente</strong></li></ul></li>";
    echo "<li><strong>Prodotto CON varianti:</strong>";
    echo "<ul><li>Il prezzo del prodotto deve essere <strong>completamente nascosto</strong></li>";
    echo "<li>Il prezzo appare solo quando selezioni una variante</li></ul></li>";
    echo "<li><strong>Varianti senza immagine:</strong>";
    echo "<ul><li>NON deve apparire il placeholder della fotocamera</li>";
    echo "<li>La variante deve avere un layout ottimizzato senza immagine</li></ul></li>";
    echo "<li><strong>Varianti con immagine:</strong>";
    echo "<ul><li>L'immagine deve essere visibile normalmente</li></ul></li>";
    echo "</ol>";
    echo "<p><em>Nota: Ora il prezzo è completamente nascosto quando ci sono varianti, non solo sostituito con un messaggio.</em></p>";

    echo "<h3>🔍 Test Immediato Prezzo</h3>";
    echo "<p><strong>SOLUZIONE IMPLEMENTATA:</strong> Il filtro del prezzo ora viene applicato solo quando necessario</p>";
    echo "<ol>";
    echo "<li><strong>Prodotti SENZA varianti:</strong> Il prezzo dovrebbe essere <span style='color: green; font-weight: bold;'>VISIBILE</span></li>";
    echo "<li><strong>Prodotti CON varianti:</strong> Il prezzo dovrebbe essere <span style='color: red; font-weight: bold;'>NASCOSTO</span></li>";
    echo "<li><strong>Variante selezionata:</strong> Il prezzo della variante dovrebbe apparire</li>";
    echo "</ol>";
    echo "<p><strong>Se il problema persiste:</strong></p>";
    echo "<ul>";
    echo "<li>Abilita WP_DEBUG nel wp-config.php</li>";
    echo "<li>Controlla i log per messaggi 'CPV Debug:'</li>";
    echo "<li>Usa il test rapido qui sotto</li>";
    echo "</ul>";

    // Test rapido per verificare il conteggio varianti
    if (isset($_GET['test_product_id'])) {
        $test_product_id = intval($_GET['test_product_id']);
        echo "<h3>🧪 Test Rapido Prodotto ID: $test_product_id</h3>";

        // Ottieni l'istanza del frontend
        $frontend = Custom_Product_Variants_Frontend::get_instance();
        $database = Custom_Product_Variants_Database::get_instance();

        $variant_count = $database->count_product_variants($test_product_id, true);
        $has_variants = $frontend->product_has_variants($test_product_id);

        echo "<p><strong>Risultati:</strong></p>";
        echo "<ul>";
        echo "<li>Conteggio varianti attive: <strong>$variant_count</strong></li>";
        echo "<li>Ha varianti: <strong>" . ($has_variants ? 'SÌ' : 'NO') . "</strong></li>";
        echo "<li>Il prezzo dovrebbe essere: <strong>" . ($has_variants ? 'NASCOSTO' : 'VISIBILE') . "</strong></li>";
        echo "</ul>";

        if ($variant_count > 0) {
            $variants = $database->get_product_variants($test_product_id, true);
            echo "<p><strong>Varianti trovate:</strong></p>";
            echo "<ul>";
            foreach ($variants as $variant) {
                echo "<li>{$variant->variant_name} - " . wc_price($variant->variant_price) . " (Attiva: " . ($variant->is_active ? 'SÌ' : 'NO') . ")</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p><strong>Test rapido:</strong> Aggiungi <code>&test_product_id=123</code> all'URL (sostituisci 123 con l'ID del prodotto da testare)</p>";
    }
}

// Esegui il test solo se richiesto tramite URL
if (isset($_GET['cpv_test']) && $_GET['cpv_test'] === 'run' && current_user_can('manage_options')) {
    add_action('wp_loaded', 'cpv_test_functionality');
}
