<?php
/**
 * File di test per verificare le funzionalità del plugin
 * Questo file può essere eseguito temporaneamente per testare le modifiche
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

// Test delle funzionalità implementate
function cpv_test_functionality() {
    echo "<h2>Test Funzionalità Plugin Custom Product Variants</h2>";
    
    // Test 1: Verifica che le classi esistano
    echo "<h3>Test 1: Verifica Classi</h3>";
    if (class_exists('CPV_Admin')) {
        echo "✅ Classe CPV_Admin trovata<br>";
    } else {
        echo "❌ Classe CPV_Admin non trovata<br>";
    }
    
    if (class_exists('CPV_Frontend')) {
        echo "✅ Classe CPV_Frontend trovata<br>";
    } else {
        echo "❌ Classe CPV_Frontend non trovata<br>";
    }
    
    if (class_exists('CPV_Database')) {
        echo "✅ Classe CPV_Database trovata<br>";
    } else {
        echo "❌ Classe CPV_Database non trovata<br>";
    }
    
    // Test 2: Verifica che la tabella del database esista
    echo "<h3>Test 2: Verifica Database</h3>";
    if (class_exists('CPV_Database')) {
        $database = new CPV_Database();
        if ($database->table_exists()) {
            echo "✅ Tabella database trovata<br>";
            
            // Mostra informazioni sulla tabella
            $table_info = $database->get_table_info();
            echo "📊 Righe nella tabella: " . $table_info['row_count'] . "<br>";
        } else {
            echo "❌ Tabella database non trovata<br>";
        }
    }
    
    // Test 3: Verifica hook WordPress
    echo "<h3>Test 3: Verifica Hook</h3>";
    if (has_action('woocommerce_single_product_summary', 'display_product_variants')) {
        echo "✅ Hook frontend registrato<br>";
    } else {
        echo "❌ Hook frontend non registrato<br>";
    }
    
    if (has_action('wp_ajax_cpv_add_variant')) {
        echo "✅ Hook AJAX add_variant registrato<br>";
    } else {
        echo "❌ Hook AJAX add_variant non registrato<br>";
    }
    
    if (has_action('wp_ajax_cpv_update_variant')) {
        echo "✅ Hook AJAX update_variant registrato<br>";
    } else {
        echo "❌ Hook AJAX update_variant non registrato<br>";
    }
    
    if (has_action('wp_ajax_cpv_get_variant')) {
        echo "✅ Hook AJAX get_variant registrato<br>";
    } else {
        echo "❌ Hook AJAX get_variant non registrato<br>";
    }
    
    // Test 4: Verifica filtri
    echo "<h3>Test 4: Verifica Filtri</h3>";
    if (has_filter('woocommerce_get_price_html')) {
        echo "✅ Filtro prezzo registrato<br>";
    } else {
        echo "❌ Filtro prezzo non registrato<br>";
    }
    
    // Test 5: Verifica JavaScript
    echo "<h3>Test 5: Verifica JavaScript</h3>";
    echo "<p>Per testare il JavaScript:</p>";
    echo "<ol>";
    echo "<li>Vai alla pagina di modifica di un prodotto</li>";
    echo "<li>Apri la console del browser (F12)</li>";
    echo "<li>Clicca sul tab 'Varianti Prodotto'</li>";
    echo "<li>Verifica che non ci siano errori JavaScript</li>";
    echo "<li>Se hai varianti esistenti, clicca 'Modifica' e verifica che il modal si apra</li>";
    echo "<li>Testa l'upload dell'immagine nel modal - dovrebbe caricare nell'anteprima del modal, non nel form di creazione</li>";
    echo "</ol>";

    echo "<h3>✅ Test completati!</h3>";
    echo "<p><strong>Note:</strong> Se tutti i test sono passati, le modifiche sono state implementate correttamente.</p>";

    echo "<h3>🐛 Debug Upload Immagini</h3>";
    echo "<p>Se l'upload delle immagini nel modal non funziona correttamente:</p>";
    echo "<ol>";
    echo "<li>Apri la console del browser</li>";
    echo "<li>Clicca 'Modifica' su una variante</li>";
    echo "<li>Clicca 'Carica Immagine' nel modal</li>";
    echo "<li>Verifica i log nella console che iniziano con 'CPV:'</li>";
    echo "<li>Dovrebbe mostrare 'isEditModal: true' quando clicchi nel modal</li>";
    echo "</ol>";
}

// Esegui il test solo se richiesto tramite URL
if (isset($_GET['cpv_test']) && $_GET['cpv_test'] === 'run' && current_user_can('manage_options')) {
    add_action('wp_loaded', 'cpv_test_functionality');
}
